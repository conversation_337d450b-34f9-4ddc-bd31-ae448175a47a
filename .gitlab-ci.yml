stages:
  - build

variables:
  DOCKER_DRIVER: overlay2
  DOCKER_TLS_CERTDIR: ""
  DOCKER_BUILDKIT: 1
  # 启用Docker层缓存
  BUILDKIT_INLINE_CACHE: 1

docker-build-push:
  stage: build
  image: docker:latest
  tags:
    - cloud-dev
  services:
    - name: docker:dind
      alias: docker
      command: ["--insecure-registry=$HARBOR_REGISTRY"]
  before_script:
    - mkdir -p ~/.docker
    - |
      cat > ~/.docker/config.json << EOF
      {
        "experimental": "enabled",
        "insecure-registries": ["$HARBOR_REGISTRY"]
      }
      EOF
    # Login to Harbor registry
    - echo $HARBOR_PASSWORD | docker login $HARBOR_REGISTRY -u $HARBOR_USERNAME --password-stdin
  script:
    # 尝试拉取之前的镜像作为缓存
    - docker pull $HARBOR_REGISTRY/$HARBOR_PROJECT/$HARBOR_IMAGE_NAME:latest || true

    # Build Docker image with cache optimization
    - |
      docker build \
        --add-host maven.yunzheng.work:************ \
        --cache-from $HARBOR_REGISTRY/$HARBOR_PROJECT/$HARBOR_IMAGE_NAME:latest \
        --build-arg BUILDKIT_INLINE_CACHE=1 \
        -t $HARBOR_REGISTRY/$HARBOR_PROJECT/$HARBOR_IMAGE_NAME:$CI_COMMIT_SHORT_SHA .

    # Tag the same image as latest
    - docker tag $HARBOR_REGISTRY/$HARBOR_PROJECT/$HARBOR_IMAGE_NAME:$CI_COMMIT_SHORT_SHA $HARBOR_REGISTRY/$HARBOR_PROJECT/$HARBOR_IMAGE_NAME:latest

    # Push both tags to insecure registry
    - docker push $HARBOR_REGISTRY/$HARBOR_PROJECT/$HARBOR_IMAGE_NAME:$CI_COMMIT_SHORT_SHA
    - docker push $HARBOR_REGISTRY/$HARBOR_PROJECT/$HARBOR_IMAGE_NAME:latest

    # Output success message
    - echo "Successfully pushed $HARBOR_REGISTRY/$HARBOR_PROJECT/$HARBOR_IMAGE_NAME:$CI_COMMIT_SHORT_SHA"
    - echo "Successfully pushed $HARBOR_REGISTRY/$HARBOR_PROJECT/$HARBOR_IMAGE_NAME:latest"
  only:
    - dev
    - main