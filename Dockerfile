# Build stage
FROM eclipse-temurin:21-jdk-alpine AS builder

# Use China mirror for faster package installation
RUN sed -i 's/dl-cdn.alpinelinux.org/mirrors.aliyun.com/g' /etc/apk/repositories && \
    apk update && \
    apk add --no-cache maven

# Set working directory
WORKDIR /app

# Copy Maven settings for private repository
RUN mkdir -p /root/.m2
COPY build/settings.xml /root/.m2/settings.xml

# Copy POM files first for better layer caching
COPY pom.xml .
COPY server/pom.xml server/

# Download dependencies with optimized caching strategy
# Use BUILD_ARG to control cache behavior
ARG USE_CACHE=true
RUN --mount=type=cache,target=/root/.m2/repository \
    if [ "$USE_CACHE" = "true" ]; then \
      mvn dependency:go-offline -B --settings /root/.m2/settings.xml \
        -Dmaven.repo.local=/root/.m2/repository \
        -Dorg.slf4j.simpleLogger.log.org.apache.maven.cli.transfer.Slf4jMavenTransferListener=warn; \
    else \
      mvn dependency:go-offline -B --settings /root/.m2/settings.xml -U \
        -Dmaven.repo.local=/root/.m2/repository \
        -Dorg.slf4j.simpleLogger.log.org.apache.maven.cli.transfer.Slf4jMavenTransferListener=warn; \
    fi

# Resolve SNAPSHOT dependencies with reduced frequency
RUN --mount=type=cache,target=/root/.m2/repository \
    mvn dependency:resolve -DincludeScope=runtime -B --settings /root/.m2/settings.xml \
      -Dmaven.repo.local=/root/.m2/repository \
      -Dorg.slf4j.simpleLogger.log.org.apache.maven.cli.transfer.Slf4jMavenTransferListener=warn || true

# Copy source code
COPY . .

# Build the application with optimized settings
RUN --mount=type=cache,target=/root/.m2/repository \
    if [ "$USE_CACHE" = "true" ]; then \
      mvn clean package -DskipTests --settings /root/.m2/settings.xml \
        -Dmaven.repo.local=/root/.m2/repository \
        -Dorg.slf4j.simpleLogger.log.org.apache.maven.cli.transfer.Slf4jMavenTransferListener=warn \
        --batch-mode --no-transfer-progress; \
    else \
      mvn clean package -DskipTests --settings /root/.m2/settings.xml -U \
        -Dmaven.repo.local=/root/.m2/repository \
        -Dorg.slf4j.simpleLogger.log.org.apache.maven.cli.transfer.Slf4jMavenTransferListener=warn \
        --batch-mode --no-transfer-progress; \
    fi

# Runtime stage
FROM eclipse-temurin:21-jdk-alpine

# Create user and group
RUN addgroup -g 9292 yzuser && \
    adduser -u 9292 -G yzuser -D yzuser

# Install necessary tools
RUN apk update && \
    apk add --no-cache procps curl bash

# Copy application jar file from build stage
COPY --from=builder /app/server/target/server.jar /home/<USER>

# Start command
CMD java -jar /home/<USER>