<?xml version="1.0" encoding="UTF-8"?>
<settings xmlns="http://maven.apache.org/SETTINGS/1.0.0"
          xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
          xsi:schemaLocation="http://maven.apache.org/SETTINGS/1.0.0
          http://maven.apache.org/xsd/settings-1.0.0.xsd">

    <!-- 本地仓库路径 -->
    <localRepository>${user.home}/.m2/repository</localRepository>

    <!-- 服务器认证配置 -->
    <servers>
        <server>
            <id>maven-releases</id>
            <username>admin</username>
            <password>yunzheng2025.</password>
        </server>
        <server>
            <id>maven-snapshots</id>
            <username>admin</username>
            <password>yunzheng2025.</password>
        </server>
    </servers>

    <!-- 镜像配置（可选，用于加速下载） -->
    <mirrors>
        <!-- 阿里云镜像 - 只镜像中央仓库，不影响私服 -->
        <mirror>
            <id>aliyun-maven</id>
            <name><PERSON><PERSON> Maven Mirror</name>
            <url>https://maven.aliyun.com/repository/public</url>
            <mirrorOf>central</mirrorOf>
        </mirror>

        <!-- 如果要让私服作为主要源，可以配置如下镜像（可选） -->
        <!--
        <mirror>
            <id>yunzheng-nexus-mirror</id>
            <name>YunZheng Nexus Mirror</name>
            <url>https://maven.yunzheng.work/repository/maven-public/</url>
            <mirrorOf>*,!maven-snapshots,!maven-releases</mirrorOf>
        </mirror>
        -->
    </mirrors>

    <!-- 配置文件 -->
    <profiles>
        <!-- 私服配置 -->
        <profile>
            <id>yunzheng-nexus</id>
            <repositories>
                <repository>
                    <id>maven-public</id>
                    <name>Maven Public Repository</name>
                    <url>https://maven.yunzheng.work/repository/maven-public/</url>
                    <releases>
                        <enabled>true</enabled>
                        <updatePolicy>daily</updatePolicy>
                    </releases>
                    <snapshots>
                        <enabled>true</enabled>
                        <updatePolicy>always</updatePolicy>
                    </snapshots>
                </repository>
                <repository>
                    <id>maven-snapshots</id>
                    <name>Maven Snapshots Repository</name>
                    <url>https://maven.yunzheng.work/repository/maven-snapshots/</url>
                    <releases>
                        <enabled>false</enabled>
                    </releases>
                    <snapshots>
                        <enabled>true</enabled>
                        <updatePolicy>always</updatePolicy>
                    </snapshots>
                </repository>
                <repository>
                    <id>maven-releases</id>
                    <name>Maven Releases Repository</name>
                    <url>https://maven.yunzheng.work/repository/maven-releases/</url>
                    <releases>
                        <enabled>true</enabled>
                        <updatePolicy>daily</updatePolicy>
                    </releases>
                    <snapshots>
                        <enabled>false</enabled>
                    </snapshots>
                </repository>
            </repositories>

            <pluginRepositories>
                <pluginRepository>
                    <id>maven-public</id>
                    <name>Maven Public Repository</name>
                    <url>https://maven.yunzheng.work/repository/maven-public/</url>
                    <releases>
                        <enabled>true</enabled>
                    </releases>
                    <snapshots>
                        <enabled>true</enabled>
                    </snapshots>
                </pluginRepository>
            </pluginRepositories>
        </profile>

        <!-- JDK 版本配置 -->
        <profile>
            <id>jdk-21</id>
            <activation>
                <activeByDefault>true</activeByDefault>
                <jdk>21</jdk>
            </activation>
            <properties>
                <maven.compiler.source>21</maven.compiler.source>
                <maven.compiler.target>21</maven.compiler.target>
                <maven.compiler.compilerVersion>21</maven.compiler.compilerVersion>
            </properties>
        </profile>
    </profiles>

    <!-- 激活的配置文件 -->
    <activeProfiles>
        <activeProfile>yunzheng-nexus</activeProfile>
        <activeProfile>jdk-21</activeProfile>
    </activeProfiles>

</settings>
